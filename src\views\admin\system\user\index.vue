<script setup>
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight, Plus } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
import { getUserListApi, resetUserPasswordApi } from "@/apis/user-management";

const userStore = useUserStore()

// 搜索表单数据
const searchForm = ref({
  userType: "", // 用户类型
  searchKeyword: "", // 手机号/姓名搜索
});

// 根据当前用户角色获取用户类型选项
const getUserTypeOptions = () => {
  const currentRoles = userStore.roles;

  // 省级用户：可以看到地市用户、县区用户
  if (currentRoles.includes('provincial')) {
    return [
      { label: "全部", value: "" },
      { label: "地市用户", value: "municipal" },
      { label: "县区用户", value: "county" },
    ];
  }

  // 市级用户：可以看到本市区、县级用户
  if (currentRoles.includes('municipal')) {
    return [
      { label: "全部", value: "" },
      { label: "本市区", value: "municipal" },
      { label: "县级用户", value: "county" },
    ];
  }

  // 县区级用户：无用户类型选项
  return [];
};

// 用户类型选项
const userTypeOptions = computed(() => getUserTypeOptions());

// 是否显示用户类型选择
const showUserTypeSelect = computed(() => {
  const currentRoles = userStore.roles;
  return currentRoles.includes('provincial') || currentRoles.includes('municipal');
});

// 搜索功能
const handleSearch = async () => {
  try {
    console.log("搜索参数:", searchForm.value);
    // 构建查询参数
    const params = {
      userType: searchForm.value.userType,
      keyword: searchForm.value.searchKeyword,
      page: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
    };

    // 调用API获取用户列表
    // const { data } = await getUserListApi(params);
    // tableData.value = data.list;
    // pagination.value.total = data.total;

    ElMessage.success("搜索完成");
  } catch (error) {
    ElMessage.error("搜索失败");
    console.error("搜索错误:", error);
  }
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    userType: "",
    searchKeyword: "",
  };
  console.log("表单已重置");
  // 重置后重新搜索
  handleSearch();
};

// 表格数据
const tableData = ref([
  {
    id: "10001",
    name: "丁汉雄",
    phone: "15257954678",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10002",
    name: "卢木仲",
    phone: "13991955044",
    role: "县区用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10003",
    name: "谢彦文",
    phone: "13112206029",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10004",
    name: "王美珠",
    phone: "13488998888",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10005",
    name: "李雅雅",
    phone: "13936729999",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10006",
    name: "吴韵如",
    phone: "15205211783",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10007",
    name: "王林萍",
    phone: "13592357387",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10008",
    name: "郑昌梦",
    phone: "18268559855",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10009",
    name: "曹敏倩",
    phone: "18212078564",
    role: "地市用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
  {
    id: "10010",
    name: "侯怡芳",
    phone: "15576592483",
    role: "县区用户",
    city: "南京市",
    district: "鼓楼区",
    unit: "鼓楼区教育局",
  },
]);

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 50,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  console.log("当前页:", page);
};

// 刷新数据
const handleRefresh = () => {
  console.log("刷新数据");
  handleSearch();
};

// 新增用户
const handleAddUser = () => {
  console.log("新增用户");
  ElMessage.info("新增用户功能");
  // TODO: 实现新增用户功能
  // 可以打开新增用户对话框或跳转到新增页面
};

// 导出数据
const handleExport = () => {
  console.log("导出数据");
  ElMessage.success("数据导出成功");
};

// 操作处理
const handleOperation = async (row, action) => {
  if (action === 'edit') {
    console.log('编辑用户:', row);
    ElMessage.info(`编辑用户: ${row.name}`);
    // TODO: 实现编辑用户功能
    // 可以打开编辑对话框或跳转到编辑页面
  } else if (action === 'reset-password') {
    try {
      await ElMessageBox.confirm(
        `确定要重置用户 ${row.name} 的密码吗？`,
        '重置密码确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );

      // 调用重置密码API
      // await resetUserPasswordApi(row.id);
      ElMessage.success(`用户 ${row.name} 的密码已重置`);
      console.log('重置密码成功:', row);
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('重置密码失败');
        console.error('重置密码错误:', error);
      }
    }
  }
};

// 获取角色CSS类名
const getRoleClass = (role) => {
  const classMap = {
    地市用户: "role-municipal",
    县区用户: "role-county",
    省级用户: "role-provincial",
  };
  return classMap[role] || "role-default";
};
</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="用户管理"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" :class="['search-form-3', showUserTypeSelect ? 'has-user-type' : 'no-user-type']" label-position="top">
        <!-- 用户类型选择 -->
        <el-form-item v-if="showUserTypeSelect" label="用户类型">
          <el-select v-model="searchForm.userType" placeholder="全部">
            <el-option
              v-for="item in userTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 手机号/姓名搜索 -->
        <el-form-item label="手机号/姓名搜索">
          <el-input
            v-model="searchForm.searchKeyword"
            placeholder="请输入手机号或姓名"
            style="width: 240px"
          />
        </el-form-item>

        <!-- 按钮组 -->
        <el-form-item class="hidden-label" label="按钮">
          <el-button class="common-button-3" @click="handleSearch">
            <template #icon>
              <el-icon>
                <Search />
              </el-icon>
            </template>
            查询
          </el-button>
          <el-button class="reset-button" @click="handleReset">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 操作按钮 -->
      <div class="mb-[16px] flex justify-between">
        <div class="flex gap-[16px]">
          <el-button class="common-button-4" @click="handleRefresh">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            刷新
          </el-button>
          <el-button class="common-button-3" @click="handleAddUser">
            <template #icon>
              <el-icon><Plus /></el-icon>
            </template>
            新增用户
          </el-button>
        </div>
        <el-button class="common-button-3" @click="handleExport">
          <template #icon>
            <img
              src="@/assets/images/common/export.png"
              alt=""
              width="16"
              height="16"
            />
          </template>
          导出数据
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 序号 -->
        <el-table-column
          prop="id"
          label="序号"
          width="80"
          align="center"
        />

        <!-- 姓名 -->
        <el-table-column
          prop="name"
          label="姓名"
          width="120"
          align="center"
        />

        <!-- 手机号 -->
        <el-table-column
          prop="phone"
          label="手机号"
          width="140"
          align="center"
        />

        <!-- 角色 -->
        <el-table-column label="角色" width="120" align="center">
          <template #default="{ row }">
            <span :class="['role-tag', getRoleClass(row.role)]">
              {{ row.role }}
            </span>
          </template>
        </el-table-column>

        <!-- 市区 -->
        <el-table-column
          prop="city"
          label="市区"
          width="120"
          align="center"
        />

        <!-- 县区 -->
        <el-table-column
          prop="district"
          label="县区"
          width="120"
          align="center"
        />

        <!-- 单位 -->
        <el-table-column
          prop="unit"
          label="单位"
          min-width="200"
          align="center"
        />

        <!-- 操作列 -->
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="flex-center-center gap-[16px]">
              <div
                class="pointer blue2"
                @click="handleOperation(row, 'edit')"
              >
                编辑
              </div>
              <div
                class="pointer blue2"
                @click="handleOperation(row, 'reset-password')"
              >
                重置密码
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 角色标签样式
.role-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.role-municipal {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }

  &.role-county {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  &.role-provincial {
    background-color: #fff2e8;
    color: #fa8c16;
    border: 1px solid #ffd591;
  }

  &.role-default {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #d9d9d9;
  }
}
</style>
