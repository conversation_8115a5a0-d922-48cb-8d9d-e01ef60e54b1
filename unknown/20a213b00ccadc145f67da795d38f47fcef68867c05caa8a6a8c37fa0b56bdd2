<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分办信息表单演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 14px;
        }
        .title-icon {
            width: 4px;
            height: 20px;
            background: linear-gradient(180deg, #0ec3ed 0%, #239dde 100%);
            border-radius: 2px;
            margin-right: 12px;
        }
        .title-text {
            font-size: 14px;
            line-height: 20px;
            font-weight: 600;
            color: #2b2c33;
            padding-left: 6px;
        }
        .assignment-opinion-hint {
            font-weight: 400;
            color: #94959C;
        }
        .el-select {
            width: 100%;
        }
        .el-textarea__inner {
            border-radius: 8px;
            font-size: 16px;
            color: #2B2C33;
            line-height: 24px;
            resize: none;
        }
        .el-textarea__inner:focus {
            border-color: #0EC3ED;
            box-shadow: 0 0 0 2px rgba(14, 195, 237, 0.1);
        }
        .el-textarea__inner::placeholder {
            color: #94959C;
        }
        .button-group {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            margin-top: 24px;
        }
        .save-btn {
            background: #6c757d;
            border-color: #6c757d;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
        }
        .submit-btn {
            background: #0EC3ED;
            border-color: #0EC3ED;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <div class="section-title">
                <div class="title-icon"></div>
                <span class="title-text">分办信息</span>
            </div>
            
            <el-form ref="assignmentFormRef" :model="assignmentForm" :rules="assignmentRules" label-position="top">
                <!-- 分办单位 -->
                <el-form-item label="分办单位" prop="assignmentUnit" style="margin-bottom: 20px;">
                    <el-select v-model="assignmentForm.assignmentUnit" placeholder="请选择分办单位">
                        <el-option v-for="option in assignmentUnitOptions" :key="option.value" :label="option.label" :value="option.value" />
                    </el-select>
                </el-form-item>

                <!-- 分办意见 -->
                <el-form-item prop="assignmentOpinion" style="margin-bottom: 20px;">
                    <template #label>
                        <span>
                            分办意见
                            <span class="assignment-opinion-hint">（不超过100字）</span>
                        </span>
                    </template>
                    <el-input v-model="assignmentForm.assignmentOpinion" type="textarea" :rows="4" 
                        placeholder="请输入分办意见..." maxlength="100" show-word-limit />
                </el-form-item>
            </el-form>

            <div class="button-group">
                <el-button class="save-btn" @click="saveDraft">保存</el-button>
                <el-button class="submit-btn" @click="submitAssignment">提交</el-button>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                // 分办表单数据
                const assignmentForm = ref({
                    assignmentUnit: '',
                    assignmentOpinion: ''
                });

                // 分办表单验证规则
                const assignmentRules = {
                    assignmentUnit: [
                        { required: true, message: '请选择分办单位', trigger: 'change' }
                    ],
                    assignmentOpinion: [
                        { required: true, message: '请输入分办意见', trigger: 'blur' },
                        { max: 100, message: '分办意见不能超过100个字符', trigger: 'blur' }
                    ]
                };

                // 分办单位选项
                const assignmentUnitOptions = [
                    { label: '南京市教育局', value: 'nanjing_education' },
                    { label: '苏州市教育局', value: 'suzhou_education' },
                    { label: '无锡市教育局', value: 'wuxi_education' },
                    { label: '常州市教育局', value: 'changzhou_education' },
                    { label: '南通市教育局', value: 'nantong_education' },
                    { label: '连云港市教育局', value: 'lianyungang_education' },
                    { label: '淮安市教育局', value: 'huaian_education' },
                    { label: '盐城市教育局', value: 'yancheng_education' },
                    { label: '扬州市教育局', value: 'yangzhou_education' },
                    { label: '镇江市教育局', value: 'zhenjiang_education' },
                    { label: '泰州市教育局', value: 'taizhou_education' },
                    { label: '宿迁市教育局', value: 'suqian_education' },
                    { label: '徐州市教育局', value: 'xuzhou_education' }
                ];

                const assignmentFormRef = ref();

                // 提交分办
                const submitAssignment = async () => {
                    try {
                        await assignmentFormRef.value.validate();
                        console.log('提交分办:', assignmentForm.value);
                        ElMessage.success('分办提交成功');
                        
                        // 重置表单
                        assignmentForm.value = {
                            assignmentUnit: '',
                            assignmentOpinion: ''
                        };
                    } catch (error) {
                        console.error('分办表单验证失败:', error);
                    }
                };

                // 保存草稿
                const saveDraft = () => {
                    console.log('保存草稿:', assignmentForm.value);
                    ElMessage.success('草稿保存成功');
                };

                return {
                    assignmentForm,
                    assignmentRules,
                    assignmentUnitOptions,
                    assignmentFormRef,
                    submitAssignment,
                    saveDraft
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
